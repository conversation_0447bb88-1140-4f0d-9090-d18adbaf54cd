//+------------------------------------------------------------------+
//|                                        Auto TP SL Manager.mq5 |
//|                        Copyright 2024, Auto TP SL Manager      |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Auto TP SL Manager"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Automatically sets TP and SL for manually opened trades on this chart only"

//--- Include trade class
#include <Trade\Trade.mqh>

//--- Input parameters
input group "=== TP/SL Settings ==="
input double InpTakeProfitPips = 10.0;        // Take Profit in Pips
input double InpStopLossPips = 5.0;           // Stop Loss in Pips
input bool InpOnlyThisSymbol = true;          // Only manage trades for this symbol
input int InpMagicNumber = 0;                 // Magic Number (0 = all manual trades)

input group "=== Safety Settings ==="
input bool InpOverwriteExisting = false;     // Overwrite existing TP/SL
input bool InpEnableLogging = true;          // Enable detailed logging

//--- Global variables
CTrade trade;
datetime last_check_time = 0;

//--- Structure to track processed positions
struct ProcessedPosition
{
    ulong ticket;
    datetime processed_time;
};

ProcessedPosition processed_positions[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set magic number for trade class (not used for position selection)
    trade.SetExpertMagicNumber(999999);
    trade.SetDeviationInPoints(10);
    
    // Initialize processed positions array
    ArrayResize(processed_positions, 0);
    
    if(InpEnableLogging)
    {
        Print("=== Auto TP/SL Manager Initialized ===");
        Print("Symbol: ", _Symbol);
        Print("Take Profit: ", InpTakeProfitPips, " pips");
        Print("Stop Loss: ", InpStopLossPips, " pips");
        Print("Magic Number Filter: ", InpMagicNumber, " (0 = all manual trades)");
        Print("Only This Symbol: ", InpOnlyThisSymbol ? "Yes" : "No");
        Print("Overwrite Existing: ", InpOverwriteExisting ? "Yes" : "No");
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(InpEnableLogging)
        Print("Auto TP/SL Manager deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for new positions every second to avoid excessive processing
    if(TimeCurrent() - last_check_time < 1) return;
    last_check_time = TimeCurrent();
    
    // Clean up old processed positions (older than 1 hour)
    CleanupProcessedPositions();
    
    // Check all positions for TP/SL management
    CheckAndSetTPSL();
}

//+------------------------------------------------------------------+
//| Check and set TP/SL for positions                               |
//+------------------------------------------------------------------+
void CheckAndSetTPSL()
{
    int total_positions = PositionsTotal();
    
    for(int i = 0; i < total_positions; i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket == 0) continue;
        
        if(!PositionSelectByTicket(ticket)) continue;
        
        // Filter by symbol if enabled
        if(InpOnlyThisSymbol && PositionGetString(POSITION_SYMBOL) != _Symbol)
            continue;
        
        // Filter by magic number if specified (0 means all manual trades)
        if(InpMagicNumber != 0 && PositionGetInteger(POSITION_MAGIC) != InpMagicNumber)
            continue;
        
        // Check if this position was already processed
        if(IsPositionProcessed(ticket)) continue;
        
        // Get position details
        string symbol = PositionGetString(POSITION_SYMBOL);
        ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double current_sl = PositionGetDouble(POSITION_SL);
        double current_tp = PositionGetDouble(POSITION_TP);
        double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
        
        // Check if we should process this position
        bool needs_sl = (current_sl == 0) || InpOverwriteExisting;
        bool needs_tp = (current_tp == 0) || InpOverwriteExisting;
        
        if(!needs_sl && !needs_tp) 
        {
            // Mark as processed even if no changes needed
            AddProcessedPosition(ticket);
            continue;
        }
        
        // Calculate new TP and SL
        double new_sl = 0, new_tp = 0;
        if(CalculateTPSL(symbol, pos_type, entry_price, new_sl, new_tp))
        {
            // Use existing values if we're not overwriting
            if(!InpOverwriteExisting)
            {
                if(current_sl != 0) new_sl = current_sl;
                if(current_tp != 0) new_tp = current_tp;
            }
            
            // Modify the position
            if(trade.PositionModify(ticket, new_sl, new_tp))
            {
                if(InpEnableLogging)
                {
                    Print("SUCCESS: Modified position ", ticket, " (", symbol, ")");
                    Print("  Type: ", pos_type == POSITION_TYPE_BUY ? "BUY" : "SELL");
                    Print("  Entry: ", entry_price);
                    Print("  New SL: ", new_sl, " (was: ", current_sl, ")");
                    Print("  New TP: ", new_tp, " (was: ", current_tp, ")");
                }
                
                // Mark as processed
                AddProcessedPosition(ticket);
            }
            else
            {
                if(InpEnableLogging)
                {
                    Print("ERROR: Failed to modify position ", ticket);
                    Print("  Error code: ", trade.ResultRetcode());
                    Print("  Description: ", trade.ResultRetcodeDescription());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate TP and SL levels                                       |
//+------------------------------------------------------------------+
bool CalculateTPSL(string symbol, ENUM_POSITION_TYPE pos_type, double entry_price, 
                   double &stop_loss, double &take_profit)
{
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(symbol, SYMBOL_DIGITS);
    
    // Convert pips to points (for 5-digit brokers, 1 pip = 10 points)
    double pip_value = point;
    if(digits == 5 || digits == 3) pip_value = point * 10;
    
    if(pos_type == POSITION_TYPE_BUY)
    {
        stop_loss = entry_price - (InpStopLossPips * pip_value);
        take_profit = entry_price + (InpTakeProfitPips * pip_value);
    }
    else // SELL
    {
        stop_loss = entry_price + (InpStopLossPips * pip_value);
        take_profit = entry_price - (InpTakeProfitPips * pip_value);
    }
    
    // Normalize prices
    stop_loss = NormalizeDouble(stop_loss, digits);
    take_profit = NormalizeDouble(take_profit, digits);
    
    // Validate levels
    double min_stop_level = SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL) * point;
    double current_price = (pos_type == POSITION_TYPE_BUY) ? 
                          SymbolInfoDouble(symbol, SYMBOL_BID) : 
                          SymbolInfoDouble(symbol, SYMBOL_ASK);
    
    if(pos_type == POSITION_TYPE_BUY)
    {
        if(stop_loss >= current_price - min_stop_level)
        {
            if(InpEnableLogging)
                Print("WARNING: SL too close to current price for BUY position");
            return false;
        }
        if(take_profit <= current_price + min_stop_level)
        {
            if(InpEnableLogging)
                Print("WARNING: TP too close to current price for BUY position");
            return false;
        }
    }
    else
    {
        if(stop_loss <= current_price + min_stop_level)
        {
            if(InpEnableLogging)
                Print("WARNING: SL too close to current price for SELL position");
            return false;
        }
        if(take_profit >= current_price - min_stop_level)
        {
            if(InpEnableLogging)
                Print("WARNING: TP too close to current price for SELL position");
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if position was already processed                          |
//+------------------------------------------------------------------+
bool IsPositionProcessed(ulong ticket)
{
    for(int i = 0; i < ArraySize(processed_positions); i++)
    {
        if(processed_positions[i].ticket == ticket)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Add position to processed list                                   |
//+------------------------------------------------------------------+
void AddProcessedPosition(ulong ticket)
{
    int size = ArraySize(processed_positions);
    ArrayResize(processed_positions, size + 1);

    processed_positions[size].ticket = ticket;
    processed_positions[size].processed_time = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Clean up old processed positions                                 |
//+------------------------------------------------------------------+
void CleanupProcessedPositions()
{
    datetime current_time = TimeCurrent();
    int cleanup_threshold = 3600; // 1 hour

    for(int i = ArraySize(processed_positions) - 1; i >= 0; i--)
    {
        if(current_time - processed_positions[i].processed_time > cleanup_threshold)
        {
            // Remove this entry by shifting array
            for(int j = i; j < ArraySize(processed_positions) - 1; j++)
            {
                processed_positions[j] = processed_positions[j + 1];
            }
            ArrayResize(processed_positions, ArraySize(processed_positions) - 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Trade transaction function - detects new manual trades          |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    // Check for new position opened (deal add)
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
    {
        // Check if it's a position opening deal
        if(HistoryDealSelect(trans.deal))
        {
            ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(trans.deal, DEAL_TYPE);

            if(deal_type == DEAL_TYPE_BUY || deal_type == DEAL_TYPE_SELL)
            {
                string deal_symbol = HistoryDealGetString(trans.deal, DEAL_SYMBOL);
                long deal_magic = HistoryDealGetInteger(trans.deal, DEAL_MAGIC);

                // Filter by symbol if enabled
                if(InpOnlyThisSymbol && deal_symbol != _Symbol)
                    return;

                // Filter by magic number if specified
                if(InpMagicNumber != 0 && deal_magic != InpMagicNumber)
                    return;

                if(InpEnableLogging)
                {
                    Print("NEW TRADE DETECTED: ", deal_symbol, " Type: ",
                          deal_type == DEAL_TYPE_BUY ? "BUY" : "SELL",
                          " Magic: ", deal_magic);
                }

                // Force immediate check for this new position
                last_check_time = 0;
            }
        }
    }
}
