//+------------------------------------------------------------------+
//|                                           Gold Scalping EA.mq5 |
//|                        Copyright 2024, Gold Scalping Strategy   |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Gold Scalping Strategy"
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input group "=== Strategy Parameters ==="
input int InpEMA_Fast = 50;                    // Fast EMA Period
input int InpEMA_Slow = 200;                   // Slow EMA Period
input int InpRSI_Period = 14;                  // RSI Period
input int InpATR_Period = 14;                  // ATR Period
input double InpRSI_Lower = 45.0;              // RSI Lower Bound
input double InpRSI_Upper = 55.0;              // RSI Upper Bound
input bool InpUseEngulfing = true;             // Use Engulfing Candle Pattern

input group "=== Risk Management ==="
input double InpLotSize = 0.01;                // Lot Size
input double InpATR_Multiplier_SL = 2.0;       // ATR Multiplier for Stop Loss
input double InpATR_Multiplier_TP = 3.0;       // ATR Multiplier for Take Profit
input int InpMagicNumber = 123456;             // Magic Number

input group "=== Trade Management ==="
input bool InpAllowBuy = true;                 // Allow Buy Orders
input bool InpAllowSell = true;                // Allow Sell Orders
input bool InpPerSymbolLimit = true;           // Per Symbol Position Limit (true) or Global Limit (false)
input int InpMaxPositions = 1;                 // Maximum Positions

input group "=== Trailing Stop ==="
input bool InpUseTrailingStop = true;          // Enable Trailing Stop
input double InpTrailingStopDistance = 50.0;   // Trailing Stop Distance (points)
input double InpTrailingStepSize = 10.0;       // Trailing Step Size (points)
input bool InpTrailingOnlyInProfit = true;     // Trail Only When In Profit

input group "=== Partial Close ==="
input bool InpUsePartialClose = true;          // Enable Partial Close
input double InpPartialClosePercent = 50.0;    // Percentage of Position to Close (%)
input double InpPartialCloseTrigger = 50.0;    // Trigger at % of TP Distance (%)

//--- Global variables
int handle_ema_fast, handle_ema_slow, handle_rsi, handle_atr;
double ema_fast[], ema_slow[], rsi[], atr[];
bool new_bar_flag = false;

//--- Partial close tracking
ulong partially_closed_tickets[];

//--- Trade management
#include <Trade\Trade.mqh>
CTrade trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize indicators
    handle_ema_fast = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    handle_ema_slow = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    handle_rsi = iRSI(_Symbol, PERIOD_CURRENT, InpRSI_Period, PRICE_CLOSE);
    handle_atr = iATR(_Symbol, PERIOD_CURRENT, InpATR_Period);
    
    // Check if indicators are created successfully
    if(handle_ema_fast == INVALID_HANDLE || handle_ema_slow == INVALID_HANDLE || 
       handle_rsi == INVALID_HANDLE || handle_atr == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    // Set magic number for trade class
    trade.SetExpertMagicNumber(InpMagicNumber);
    
    // Set array as series
    ArraySetAsSeries(ema_fast, true);
    ArraySetAsSeries(ema_slow, true);
    ArraySetAsSeries(rsi, true);
    ArraySetAsSeries(atr, true);
    
    Print("Gold Scalping EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(handle_ema_fast);
    IndicatorRelease(handle_ema_slow);
    IndicatorRelease(handle_rsi);
    IndicatorRelease(handle_atr);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Handle partial close for existing positions
    if(InpUsePartialClose)
    {
        ManagePartialClose();
    }

    // Handle trailing stop for existing positions
    if(InpUseTrailingStop)
    {
        ManageTrailingStop();
    }

    // Check for new bar
    if(!IsNewBar()) return;

    Print("New bar detected - checking for signals");

    // Get current indicator values
    if(!GetIndicatorValues())
    {
        Print("Failed to get indicator values");
        return;
    }

    // Check current positions
    int current_positions = CountPositions();
    string limit_type = InpPerSymbolLimit ? "per symbol" : "global";
    Print("Current positions: ", current_positions, " Max allowed: ", InpMaxPositions, " (", limit_type, ")");
    if(current_positions >= InpMaxPositions)
    {
        Print("Maximum positions reached (", limit_type, " limit)");
        return;
    }

    // Get current market data
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, 0, 3, rates) < 3)
    {
        Print("Failed to get market data");
        return;
    }
    ArraySetAsSeries(rates, true);

    Print("Checking signals - EMA Fast: ", ema_fast[0], " EMA Slow: ", ema_slow[0], " RSI: ", rsi[0]);

    // Check for trade signals
    CheckForBuySignal(rates);
    CheckForSellSignal(rates);
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    static datetime last_time = 0;
    datetime current_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(current_time != last_time)
    {
        last_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get indicator values                                             |
//+------------------------------------------------------------------+
bool GetIndicatorValues()
{
    // Copy indicator values
    if(CopyBuffer(handle_ema_fast, 0, 0, 3, ema_fast) < 3) return false;
    if(CopyBuffer(handle_ema_slow, 0, 0, 3, ema_slow) < 3) return false;
    if(CopyBuffer(handle_rsi, 0, 0, 3, rsi) < 3) return false;
    if(CopyBuffer(handle_atr, 0, 0, 3, atr) < 3) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for buy signal                                             |
//+------------------------------------------------------------------+
void CheckForBuySignal(const MqlRates &rates[])
{
    if(!InpAllowBuy)
    {
        Print("Buy signals disabled");
        return;
    }

    // Check trend condition: 50 EMA > 200 EMA
    if(ema_fast[0] <= ema_slow[0])
    {
        Print("Buy rejected: EMA trend not bullish. Fast EMA: ", ema_fast[0], " Slow EMA: ", ema_slow[0]);
        return;
    }

    // Check RSI neutral zone (45-55)
    if(rsi[0] < InpRSI_Lower || rsi[0] > InpRSI_Upper)
    {
        Print("Buy rejected: RSI outside neutral zone. RSI: ", rsi[0], " Range: ", InpRSI_Lower, "-", InpRSI_Upper);
        return;
    }

    // Check for bullish engulfing pattern (if enabled)
    if(InpUseEngulfing && !IsBullishEngulfing(rates))
    {
        Print("Buy rejected: No bullish engulfing pattern");
        return;
    }

    // Check if price closed above 50 EMA
    if(rates[0].close <= ema_fast[0])
    {
        Print("Buy rejected: Price not above fast EMA. Close: ", rates[0].close, " Fast EMA: ", ema_fast[0]);
        return;
    }

    Print("All buy conditions met - executing buy order");
    // Execute buy order
    ExecuteBuyOrder();
}

//+------------------------------------------------------------------+
//| Check for sell signal                                            |
//+------------------------------------------------------------------+
void CheckForSellSignal(const MqlRates &rates[])
{
    if(!InpAllowSell)
    {
        Print("Sell signals disabled");
        return;
    }

    // Check trend condition: 50 EMA < 200 EMA
    if(ema_fast[0] >= ema_slow[0])
    {
        Print("Sell rejected: EMA trend not bearish. Fast EMA: ", ema_fast[0], " Slow EMA: ", ema_slow[0]);
        return;
    }

    // Check RSI neutral zone (45-55)
    if(rsi[0] < InpRSI_Lower || rsi[0] > InpRSI_Upper)
    {
        Print("Sell rejected: RSI outside neutral zone. RSI: ", rsi[0], " Range: ", InpRSI_Lower, "-", InpRSI_Upper);
        return;
    }

    // Check for bearish engulfing pattern (if enabled)
    if(InpUseEngulfing && !IsBearishEngulfing(rates))
    {
        Print("Sell rejected: No bearish engulfing pattern");
        return;
    }

    // Check if price closed below 50 EMA
    if(rates[0].close >= ema_fast[0])
    {
        Print("Sell rejected: Price not below fast EMA. Close: ", rates[0].close, " Fast EMA: ", ema_fast[0]);
        return;
    }

    Print("All sell conditions met - executing sell order");
    // Execute sell order
    ExecuteSellOrder();
}

//+------------------------------------------------------------------+
//| Check for bullish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBullishEngulfing(const MqlRates &rates[])
{
    // Current candle should be bullish
    if(rates[0].close <= rates[0].open) return false;
    
    // Previous candle should be bearish
    if(rates[1].close >= rates[1].open) return false;
    
    // Current candle should engulf previous candle
    if(rates[0].open >= rates[1].close && rates[0].close >= rates[1].open)
        return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| Check for bearish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBearishEngulfing(const MqlRates &rates[])
{
    // Current candle should be bearish
    if(rates[0].close >= rates[0].open) return false;
    
    // Previous candle should be bullish
    if(rates[1].close <= rates[1].open) return false;
    
    // Current candle should engulf previous candle
    if(rates[0].open <= rates[1].close && rates[0].close <= rates[1].open)
        return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

    // Calculate stop loss based on ATR
    double stop_loss = ask - (atr[0] * InpATR_Multiplier_SL);

    // Calculate take profit based on ATR
    double take_profit = ask + (atr[0] * InpATR_Multiplier_TP);

    // Normalize prices
    stop_loss = NormalizeDouble(stop_loss, digits);
    take_profit = NormalizeDouble(take_profit, digits);

    // Execute buy order
    if(trade.Buy(InpLotSize, _Symbol, ask, stop_loss, take_profit, "Gold Scalping Buy"))
    {
        Print("Buy order executed at ", ask, " SL: ", stop_loss, " TP: ", take_profit, " ATR: ", atr[0]);
    }
    else
    {
        Print("Failed to execute buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

    // Calculate stop loss based on ATR
    double stop_loss = bid + (atr[0] * InpATR_Multiplier_SL);

    // Calculate take profit based on ATR
    double take_profit = bid - (atr[0] * InpATR_Multiplier_TP);

    // Normalize prices
    stop_loss = NormalizeDouble(stop_loss, digits);
    take_profit = NormalizeDouble(take_profit, digits);

    // Execute sell order
    if(trade.Sell(InpLotSize, _Symbol, bid, stop_loss, take_profit, "Gold Scalping Sell"))
    {
        Print("Sell order executed at ", bid, " SL: ", stop_loss, " TP: ", take_profit, " ATR: ", atr[0]);
    }
    else
    {
        Print("Failed to execute sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Count current positions                                          |
//+------------------------------------------------------------------+
int CountPositions()
{
    int count = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                // If per-symbol limit is enabled, count only positions for current symbol
                if(InpPerSymbolLimit)
                {
                    if(PositionGetString(POSITION_SYMBOL) == _Symbol)
                    {
                        count++;
                    }
                }
                else
                {
                    // Global limit: count all positions with this magic number
                    count++;
                }
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Check if ticket is in partially closed array                    |
//+------------------------------------------------------------------+
bool IsTicketPartiallyClosed(ulong ticket)
{
    int size = ArraySize(partially_closed_tickets);
    for(int i = 0; i < size; i++)
    {
        if(partially_closed_tickets[i] == ticket)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Add ticket to partially closed array                            |
//+------------------------------------------------------------------+
void AddTicketToPartiallyClosed(ulong ticket)
{
    int size = ArraySize(partially_closed_tickets);
    ArrayResize(partially_closed_tickets, size + 1);
    partially_closed_tickets[size] = ticket;
}

//+------------------------------------------------------------------+
//| Manage partial close for existing positions                     |
//+------------------------------------------------------------------+
void ManagePartialClose()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            // Check if position belongs to this EA and symbol
            if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber) continue;
            if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

            ulong ticket = PositionGetInteger(POSITION_TICKET);

            // Skip if already partially closed
            if(IsTicketPartiallyClosed(ticket)) continue;

            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double pos_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double pos_tp = PositionGetDouble(POSITION_TP);
            double pos_volume = PositionGetDouble(POSITION_VOLUME);

            // Skip if no take profit is set
            if(pos_tp == 0) continue;

            bool should_partial_close = false;
            double current_price = 0;

            if(pos_type == POSITION_TYPE_BUY)
            {
                current_price = bid;
                // Calculate distance to TP and current progress
                double total_distance = pos_tp - pos_open_price;
                double current_progress = current_price - pos_open_price;
                double progress_percent = (current_progress / total_distance) * 100.0;

                if(progress_percent >= InpPartialCloseTrigger)
                {
                    should_partial_close = true;
                }
            }
            else if(pos_type == POSITION_TYPE_SELL)
            {
                current_price = ask;
                // Calculate distance to TP and current progress
                double total_distance = pos_open_price - pos_tp;
                double current_progress = pos_open_price - current_price;
                double progress_percent = (current_progress / total_distance) * 100.0;

                if(progress_percent >= InpPartialCloseTrigger)
                {
                    should_partial_close = true;
                }
            }

            // Execute partial close
            if(should_partial_close)
            {
                double close_volume = NormalizeDouble(pos_volume * (InpPartialClosePercent / 100.0), 2);

                if(trade.PositionClosePartial(ticket, close_volume))
                {
                    AddTicketToPartiallyClosed(ticket);
                    Print("Partial close executed for ticket ", ticket,
                          " Type: ", EnumToString(pos_type),
                          " Closed volume: ", close_volume,
                          " Remaining volume: ", pos_volume - close_volume,
                          " At price: ", current_price);
                }
                else
                {
                    Print("Failed to execute partial close for ticket ", ticket,
                          " Error: ", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage trailing stop for existing positions                     |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            // Check if position belongs to this EA and symbol
            if(PositionGetInteger(POSITION_MAGIC) != InpMagicNumber) continue;
            if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double pos_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double pos_current_sl = PositionGetDouble(POSITION_SL);
            double pos_current_tp = PositionGetDouble(POSITION_TP);

            double new_sl = 0;
            bool should_modify = false;

            if(pos_type == POSITION_TYPE_BUY)
            {
                // Calculate new stop loss for buy position
                double trailing_sl = bid - (InpTrailingStopDistance * point);

                // Check if we should only trail when in profit
                if(InpTrailingOnlyInProfit && bid <= pos_open_price)
                {
                    continue; // Skip if not in profit
                }

                // Check if new SL is better than current SL
                if(pos_current_sl == 0 || trailing_sl > pos_current_sl + (InpTrailingStepSize * point))
                {
                    new_sl = NormalizeDouble(trailing_sl, digits);
                    should_modify = true;
                }
            }
            else if(pos_type == POSITION_TYPE_SELL)
            {
                // Calculate new stop loss for sell position
                double trailing_sl = ask + (InpTrailingStopDistance * point);

                // Check if we should only trail when in profit
                if(InpTrailingOnlyInProfit && ask >= pos_open_price)
                {
                    continue; // Skip if not in profit
                }

                // Check if new SL is better than current SL
                if(pos_current_sl == 0 || trailing_sl < pos_current_sl - (InpTrailingStepSize * point))
                {
                    new_sl = NormalizeDouble(trailing_sl, digits);
                    should_modify = true;
                }
            }

            // Modify position if needed
            if(should_modify)
            {
                if(trade.PositionModify(ticket, new_sl, pos_current_tp))
                {
                    Print("Trailing stop updated for ticket ", ticket,
                          " Type: ", EnumToString(pos_type),
                          " New SL: ", new_sl,
                          " Previous SL: ", pos_current_sl);
                }
                else
                {
                    Print("Failed to update trailing stop for ticket ", ticket,
                          " Error: ", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    // Handle trade events if needed
    if(trans.symbol == _Symbol)
    {
        if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
        {
            // Get deal info to check magic number
            if(HistoryDealSelect(trans.deal))
            {
                long deal_magic = HistoryDealGetInteger(trans.deal, DEAL_MAGIC);
                if(deal_magic == InpMagicNumber)
                {
                    Print("Trade executed: ", trans.symbol, " Volume: ", trans.volume, " Price: ", trans.price);
                }
            }
        }
    }
}