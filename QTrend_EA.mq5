//+------------------------------------------------------------------+
//|                                                    QTrend_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input group "Main Settings"
input int      TrendPeriod = 200;           // Trend period
input int      ATRPeriod = 14;              // ATR Period
input double   ATRMultiplier = 1.0;         // ATR Multiplier
input bool     UseEMASmooth = false;        // Smooth source with EMA
input int      EMAPeriod = 3;               // EMA Smoother period

input group "Trading Settings"
input double   LotSize = 0.01;              // Base lot size
input bool     UseATRBasedSLTP = true;      // Use ATR-based SL/TP
input int      StopLoss = 0;                // Stop Loss in points (0 = disabled, used when ATR-based is off)
input int      TakeProfit = 0;              // Take Profit in points (0 = disabled, used when ATR-based is off)
input double   ATR_SL_Multiplier = 2.0;     // ATR multiplier for Stop Loss
input double   ATR_TP_Multiplier = 3.0;     // ATR multiplier for Take Profit
input bool     UsePartialClose = true;      // Use partial position closure
input bool     UseAutoLotSizing = false;    // Auto increase lot size on consecutive trades
input double   LotMultiplier = 2.0;         // Lot size multiplier for auto sizing
input int      MagicNumber = 12345;         // Magic number
input string   TradeComment = "QTrend_EA";  // Trade comment

//--- Global variables
int atrHandle;
int emaHandle;
double trendLine[];
string lastSignal = "";
datetime lastBarTime = 0;

//+------------------------------------------------------------------+
//| Get the appropriate filling mode for the symbol                 |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFillingMode()
{
   uint filling = (uint)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);

   if((filling & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
      return ORDER_FILLING_FOK;
   else if((filling & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
      return ORDER_FILLING_IOC;
   else
      return ORDER_FILLING_RETURN;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Create ATR indicator handle
   atrHandle = iATR(_Symbol, _Period, ATRPeriod);
   if(atrHandle == INVALID_HANDLE)
   {
      Print("Error creating ATR indicator");
      return(INIT_FAILED);
   }
   
   //--- Create EMA indicator handle if needed
   if(UseEMASmooth)
   {
      emaHandle = iMA(_Symbol, _Period, EMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
      if(emaHandle == INVALID_HANDLE)
      {
         Print("Error creating EMA indicator");
         return(INIT_FAILED);
      }
   }
   
   //--- Initialize arrays
   ArraySetAsSeries(trendLine, true);
   
   Print("QTrend EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   if(atrHandle != INVALID_HANDLE)
      IndicatorRelease(atrHandle);
   if(emaHandle != INVALID_HANDLE)
      IndicatorRelease(emaHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if new bar
   datetime currentBarTime = iTime(_Symbol, _Period, 0);
   if(currentBarTime == lastBarTime)
      return;
   lastBarTime = currentBarTime;
   
   //--- Calculate indicator values
   if(!CalculateIndicator())
      return;
   
   //--- Check for trading signals
   CheckTradingSignals();
   
   //--- Manage existing positions
   ManagePositions();
}

//+------------------------------------------------------------------+
//| Calculate Q-Trend indicator values                               |
//+------------------------------------------------------------------+
bool CalculateIndicator()
{
   //--- Get required bars
   if(Bars(_Symbol, _Period) < TrendPeriod + 10)
      return false;
   
   //--- Get ATR values
   double atrValues[];
   ArraySetAsSeries(atrValues, true);
   if(CopyBuffer(atrHandle, 0, 1, 2, atrValues) < 2)
      return false;
   
   //--- Get source prices
   double src[];
   ArraySetAsSeries(src, true);
   
   if(UseEMASmooth)
   {
      if(CopyBuffer(emaHandle, 0, 0, TrendPeriod + 10, src) < TrendPeriod + 10)
         return false;
   }
   else
   {
      if(CopyClose(_Symbol, _Period, 0, TrendPeriod + 10, src) < TrendPeriod + 10)
         return false;
   }
   
   //--- Calculate highest and lowest
   double highest = src[ArrayMaximum(src, 0, TrendPeriod)];
   double lowest = src[ArrayMinimum(src, 0, TrendPeriod)];
   double range = highest - lowest;
   
   //--- Calculate initial trend line
   double currentTrendLine = (highest + lowest) / 2.0;
   
   //--- Get previous trend line value
   ArrayResize(trendLine, ArraySize(trendLine) + 1);
   if(ArraySize(trendLine) > 1)
      currentTrendLine = trendLine[1];
   
   //--- Calculate epsilon (sensitivity measure)
   double epsilon = ATRMultiplier * atrValues[0];
   
   //--- Check for trend line changes (Type A logic)
   bool changeUp = (src[0] > currentTrendLine + epsilon) || 
                   (src[1] <= currentTrendLine + epsilon && src[0] > currentTrendLine + epsilon);
   bool changeDown = (src[0] < currentTrendLine - epsilon) || 
                     (src[1] >= currentTrendLine - epsilon && src[0] < currentTrendLine - epsilon);
   
   //--- Update trend line
   if(changeUp)
      currentTrendLine = currentTrendLine + epsilon;
   else if(changeDown)
      currentTrendLine = currentTrendLine - epsilon;
   
   //--- Store current trend line value
   trendLine[0] = currentTrendLine;
   
   //--- Check for strong signals
   double openPrice = iOpen(_Symbol, _Period, 0);
   bool strongBuy = false;
   bool strongSell = false;
   
   for(int i = 0; i < 5; i++)
   {
      double pastOpen = iOpen(_Symbol, _Period, i);
      if(pastOpen < lowest + range / 8.0 && pastOpen >= lowest)
         strongBuy = true;
      if(pastOpen > highest - range / 8.0 && pastOpen <= highest)
         strongSell = true;
   }
   
   //--- Update last signal
   if(changeUp && !strongBuy)
      lastSignal = "BUY";
   else if(changeUp && strongBuy)
      lastSignal = "STRONG_BUY";
   else if(changeDown && !strongSell)
      lastSignal = "SELL";
   else if(changeDown && strongSell)
      lastSignal = "STRONG_SELL";
   
   return true;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
   //--- Check if we have a new signal
   static string prevSignal = "";
   
   if(lastSignal == prevSignal || lastSignal == "")
      return;
   
   //--- Close opposite positions first
   if(StringFind(lastSignal, "BUY") >= 0)
      ClosePositions(POSITION_TYPE_SELL);
   else if(StringFind(lastSignal, "SELL") >= 0)
      ClosePositions(POSITION_TYPE_BUY);
   
   //--- Open new position
   if(StringFind(lastSignal, "BUY") >= 0)
      OpenPosition(ORDER_TYPE_BUY);
   else if(StringFind(lastSignal, "SELL") >= 0)
      OpenPosition(ORDER_TYPE_SELL);
   
   prevSignal = lastSignal;
}

//+------------------------------------------------------------------+
//| Open trading position                                            |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE orderType)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = CalculateLotSize();
   request.type = orderType;
   request.price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                                                   SymbolInfoDouble(_Symbol, SYMBOL_BID);
   request.deviation = 3;
   request.magic = MagicNumber;
   request.comment = TradeComment;
   request.type_filling = GetFillingMode();

   //--- Set stop loss and take profit
   if(UseATRBasedSLTP)
   {
      //--- Get current ATR value
      double atrValues[];
      ArraySetAsSeries(atrValues, true);
      if(CopyBuffer(atrHandle, 0, 1, 1, atrValues) > 0)
      {
         double atrValue = atrValues[0];

         if(orderType == ORDER_TYPE_BUY)
         {
            request.sl = request.price - (ATR_SL_Multiplier * atrValue);
            request.tp = request.price + (ATR_TP_Multiplier * atrValue);
         }
         else
         {
            request.sl = request.price + (ATR_SL_Multiplier * atrValue);
            request.tp = request.price - (ATR_TP_Multiplier * atrValue);
         }
      }
   }
   else
   {
      //--- Use fixed points SL/TP
      if(StopLoss > 0)
      {
         if(orderType == ORDER_TYPE_BUY)
            request.sl = request.price - StopLoss * _Point;
         else
            request.sl = request.price + StopLoss * _Point;
      }

      if(TakeProfit > 0)
      {
         if(orderType == ORDER_TYPE_BUY)
            request.tp = request.price + TakeProfit * _Point;
         else
            request.tp = request.price - TakeProfit * _Point;
      }
   }

   //--- Send order
   bool orderResult = OrderSend(request, result);
   if(orderResult && result.retcode == TRADE_RETCODE_DONE)
   {
      Print("Position opened: ", EnumToString(orderType), " at ", request.price,
            " | Volume: ", request.volume, " | SL: ", request.sl, " | TP: ", request.tp);
   }
   else
   {
      Print("Error opening position: ", result.retcode, " - ", result.comment);
   }
}

//+------------------------------------------------------------------+
//| Close positions of specified type                               |
//+------------------------------------------------------------------+
void ClosePositions(ENUM_POSITION_TYPE posType)
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol && 
         PositionGetInteger(POSITION_MAGIC) == MagicNumber &&
         PositionGetInteger(POSITION_TYPE) == posType)
      {
         MqlTradeRequest request = {};
         MqlTradeResult result = {};
         
         request.action = TRADE_ACTION_DEAL;
         request.symbol = _Symbol;
         request.volume = PositionGetDouble(POSITION_VOLUME);
         request.type = (posType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         request.price = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);
         request.position = PositionGetInteger(POSITION_TICKET);
         request.deviation = 3;
         request.magic = MagicNumber;
         request.type_filling = GetFillingMode();

         bool closeResult = OrderSend(request, result);
         if(!closeResult || result.retcode != TRADE_RETCODE_DONE)
         {
            Print("Error closing position: ", result.retcode, " - ", result.comment);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
   if(!UsePartialClose)
      return;
   
   //--- Check positions for partial closure
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol && 
         PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
         double takeProfit = PositionGetDouble(POSITION_TP);
         double volume = PositionGetDouble(POSITION_VOLUME);
         ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
         
         if(takeProfit > 0 && volume > 0.01) // Only if TP is set and volume allows partial close
         {
            double halfwayPrice;
            bool shouldPartialClose = false;
            
            if(posType == POSITION_TYPE_BUY)
            {
               halfwayPrice = openPrice + (takeProfit - openPrice) / 2.0;
               shouldPartialClose = (currentPrice >= halfwayPrice);
            }
            else
            {
               halfwayPrice = openPrice - (openPrice - takeProfit) / 2.0;
               shouldPartialClose = (currentPrice <= halfwayPrice);
            }
            
            if(shouldPartialClose)
            {
               // Close half position
               MqlTradeRequest request = {};
               MqlTradeResult result = {};
               
               request.action = TRADE_ACTION_DEAL;
               request.symbol = _Symbol;
               request.volume = volume / 2.0;
               request.type = (posType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
               request.price = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                                               SymbolInfoDouble(_Symbol, SYMBOL_ASK);
               request.position = PositionGetInteger(POSITION_TICKET);
               request.deviation = 3;
               request.magic = MagicNumber;
               request.comment = "Partial Close";
               request.type_filling = GetFillingMode();

               bool partialResult = OrderSend(request, result);
               if(partialResult && result.retcode == TRADE_RETCODE_DONE)
               {
                  Print("Partial close executed: ", request.volume, " lots at ", request.price);
               }
               else
               {
                  Print("Error in partial close: ", result.retcode, " - ", result.comment);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on auto sizing settings                |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   if(!UseAutoLotSizing)
      return LotSize;

   //--- Find the last opened position with floating P&L
   double lastPositionVolume = 0.0;
   datetime lastPositionTime = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol &&
         PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         datetime posTime = (datetime)PositionGetInteger(POSITION_TIME);
         if(posTime > lastPositionTime)
         {
            lastPositionTime = posTime;
            lastPositionVolume = PositionGetDouble(POSITION_VOLUME);
         }
      }
   }

   //--- If no floating position found, use base lot size
   if(lastPositionVolume == 0.0)
      return LotSize;

   //--- Calculate new lot size
   double newLotSize = lastPositionVolume * LotMultiplier;

   //--- Check broker limits
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   //--- Normalize lot size to broker's step
   newLotSize = MathFloor(newLotSize / lotStep) * lotStep;

   //--- Apply limits
   if(newLotSize < minLot)
      newLotSize = minLot;
   if(newLotSize > maxLot)
      newLotSize = maxLot;

   Print("Auto lot sizing: Last position volume = ", lastPositionVolume,
         ", New lot size = ", newLotSize);

   return newLotSize;
}
