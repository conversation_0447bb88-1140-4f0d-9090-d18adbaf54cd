//+------------------------------------------------------------------+
//|                XAUUSD_EMA_EA- V4 with Bias copy.mq5      |
//|                                                                    |
//|     EMA-based strategy for Gold trading on 1-minute chart          |
//|     Auto-detects symbol (works with XAUUSD, GOLD, etc.)            |
//|     Uses EMA crossover as a trigger to open trades                 |
//|     ALWAYS waits for a new EMA crossover after TP/SL is hit        |
//|     Only allows one position at a time (for effective martingale)   |
//|     Features: ATR-based dynamic TP/SL, Smart martingale recovery    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025"
#property link      ""
#property version   "1.00"
#property strict

// Include necessary libraries
#include <Trade\Trade.mqh>

// Forward declarations
bool IsGoldSymbol(string symbol);
bool ValidateInputParameters();
void CheckClosedPositions();
int CountOpenPositions();
void CheckEntrySignal(bool is_new_bar);
double GetEMA(int handle, int shift);
double GetClose(int shift);
void ExecuteBuyTrade();
void ExecuteSellTrade();
double GetPipValue();
double GetATR(int shift);
double CalculateFixedBuyStopLoss(double entry_price);
double CalculateFixedBuyTakeProfit(double entry_price);
double CalculateFixedSellStopLoss(double entry_price);
double CalculateFixedSellTakeProfit(double entry_price);
double CalculateATRBasedBuyStopLoss(double entry_price);
double CalculateATRBasedBuyTakeProfit(double entry_price);
double CalculateATRBasedSellStopLoss(double entry_price);
double CalculateATRBasedSellTakeProfit(double entry_price);
string ErrorDescription(int error_code);

// Global variables
CTrade trade;                   // Trade object
int g_fast_ema_handle;           // Fast EMA indicator handle
int g_slow_ema_handle;           // Slow EMA indicator handle
int g_atr_handle;              // ATR indicator handle
datetime g_last_trade_time = 0; // Time of the last trade
string g_symbol = "";          // Current chart symbol (auto-detected)
double g_pip_value;             // Global pip value

// Martingale variables
double g_current_lot_size = 0;  // Current lot size (will be initialized in OnInit)
int g_consecutive_losses = 0;   // Count of consecutive losses
ulong g_last_processed_deal_ticket = 0; // To avoid reprocessing deals

// Input parameters
// General settings
input int    Magic_Number = 123456;    // Magic number for trade identification
input double Fixed_Lot_Size = 0.01;    // Initial lot size for trades

// Strategy parameters
input int    Fast_EMA_Period = 7;      // Fast EMA period for entry signal
input int    Slow_EMA_Period = 21;     // Slow EMA period for entry signal
input double TP_Pips = 14.0;           // Take profit in pips
input double SL_Pips = 7.0;            // Stop loss in pips
input int    Min_Trade_Interval = 60;  // Minimum time between trades (seconds)

// ATR-based TP/SL parameters
input bool   Use_ATR_Based_TPSL = false; // Use ATR-based dynamic TP/SL instead of fixed pips
input int    ATR_Period = 14;            // ATR period for dynamic TP/SL calculation
input double ATR_TP_Multiplier = 2.5;    // ATR multiplier for take profit (should be > SL multiplier)
input double ATR_SL_Multiplier = 1.0;    // ATR multiplier for stop loss (should be < TP multiplier)

// Martingale parameters
input bool   Use_Martingale = true;    // Use martingale strategy to recover losses
input double Martingale_Multiplier = 2.0; // Multiplier for lot size after loss
input int    Max_Martingale_Level = 5; // Maximum martingale level (to limit risk)
input double Max_Lot_Size = 10.0;     // Maximum allowed lot size

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize trade object with magic number
   trade.SetExpertMagicNumber(Magic_Number);
   Print("Using Magic Number: ", Magic_Number, ". Make sure this is unique for each EA instance.");

   // Auto-detect the current chart symbol
   g_symbol = Symbol();

   // Auto-detect pip value
   g_pip_value = SymbolInfoDouble(g_symbol, SYMBOL_POINT) * pow(10, SymbolInfoInteger(g_symbol, SYMBOL_DIGITS) % 2);
   Print("Auto-detected Pip Value: ", DoubleToString(g_pip_value, 5));

   // Validate that we're on a gold instrument (XAUUSD, GOLD, etc.)
   if(!IsGoldSymbol(g_symbol))
   {
      Print("WARNING: This EA is designed for Gold trading. Current symbol: ", g_symbol);
      Print("The EA will continue, but please verify this is a Gold instrument.");
   }

   Print("Auto-detected symbol: ", g_symbol);

   // Validate input parameters
   if(!ValidateInputParameters())
      return INIT_PARAMETERS_INCORRECT;

   // Check for valid timeframe (M1)
   if(Period() != PERIOD_M1)
   {
      Print("This EA is designed for M1 timeframe only!");
      return INIT_FAILED;
   }

   // Create Fast EMA indicator handle for the current symbol
   g_fast_ema_handle = iMA(g_symbol, PERIOD_M1, Fast_EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   if(g_fast_ema_handle == INVALID_HANDLE)
   {
      Print("Failed to create Fast EMA indicator handle. Error: ", GetLastError());
      return INIT_FAILED;
   }

   // Create Slow EMA indicator handle for the current symbol
   g_slow_ema_handle = iMA(g_symbol, PERIOD_M1, Slow_EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   if(g_slow_ema_handle == INVALID_HANDLE)
   {
      Print("Failed to create Slow EMA indicator handle. Error: ", GetLastError());
      return INIT_FAILED;
   }

   // Create ATR indicator handle if ATR-based TP/SL is enabled
   if(Use_ATR_Based_TPSL)
   {
      g_atr_handle = iATR(g_symbol, PERIOD_M1, ATR_Period);
      if(g_atr_handle == INVALID_HANDLE)
      {
         Print("Failed to create ATR indicator handle. Error: ", GetLastError());
         return INIT_FAILED;
      }
      Print("ATR indicator initialized with period: ", ATR_Period);
   }

   // Initialize martingale variables
   g_current_lot_size = Fixed_Lot_Size;
   g_consecutive_losses = 0;
   
   // Find the last deal ticket to avoid reprocessing history on restart
   if(HistorySelect(0, TimeCurrent()))
   {
      for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == Magic_Number)
         {
            g_last_processed_deal_ticket = ticket;
            Print("Martingale: Last processed deal ticket on init: ", g_last_processed_deal_ticket);
            break; // Found the last one
         }
      }
   }

   Print("EMA EA initialized successfully. Trading on ", g_symbol, " 1-minute chart.");

   if(Use_Martingale)
      Print("Martingale strategy enabled. Initial lot size: ", DoubleToString(g_current_lot_size, 2),
            ", Multiplier: ", DoubleToString(Martingale_Multiplier, 2));

   // Log TP/SL settings
   if(Use_ATR_Based_TPSL)
      Print("ATR-based TP/SL enabled - ATR Period: ", ATR_Period,
            ", TP Multiplier: ", DoubleToString(ATR_TP_Multiplier, 1),
            ", SL Multiplier: ", DoubleToString(ATR_SL_Multiplier, 1));
   else
      Print("Fixed TP/SL enabled - TP: ", DoubleToString(TP_Pips, 1), " pips, SL: ", DoubleToString(SL_Pips, 1), " pips");

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Release indicator handles
   if(g_fast_ema_handle != INVALID_HANDLE)
      IndicatorRelease(g_fast_ema_handle);
      
   if(g_slow_ema_handle != INVALID_HANDLE)
      IndicatorRelease(g_slow_ema_handle);

   if(g_atr_handle != INVALID_HANDLE)
      IndicatorRelease(g_atr_handle);

   Print("EMA EA deinitialized for ", g_symbol);
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   static datetime last_bar_time = 0;
   datetime time[];
   if(CopyTime(_Symbol, _Period, 0, 1, time) != 1)
   {
      Print("Error copying time data");
      return;
   }
   datetime current_bar_time = time[0];

   bool is_new_bar = false;
   if(current_bar_time != last_bar_time)
   {
      last_bar_time = current_bar_time;
      is_new_bar = true;
   }

   // Check for closed positions and handle martingale
   CheckClosedPositions();

   // Check if we already have an open position
   if(CountOpenPositions() > 0)
   {
      return;
   }

   // Check for entry signals on the current symbol
   CheckEntrySignal(is_new_bar);
}

//+------------------------------------------------------------------+
//| Check for closed positions and handle martingale logic            |
//+------------------------------------------------------------------+
void CheckClosedPositions()
{
    if(HistorySelect(0, TimeCurrent()))
    {
        for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
        {
            ulong deal_ticket = HistoryDealGetTicket(i);

            // If we have seen this ticket, we can stop, as history is sorted newest to oldest.
            if (deal_ticket <= g_last_processed_deal_ticket)
            {
                break;
            }

            if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) == Magic_Number && HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
                // This is a new closing deal for our EA
                g_last_processed_deal_ticket = deal_ticket;

                double profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
                if(profit < 0)
                {
                    g_consecutive_losses++;
                    Print("Martingale: Loss detected. Consecutive losses: ", g_consecutive_losses);
                    if(g_consecutive_losses <= Max_Martingale_Level)
                    {
                        g_current_lot_size *= Martingale_Multiplier;
                        if(g_current_lot_size > Max_Lot_Size)
                        {
                            g_current_lot_size = Max_Lot_Size;
                        }
                        Print("Martingale: Next lot size: ", DoubleToString(g_current_lot_size, 2));
                    }
                    else
                    {
                        g_consecutive_losses = 0;
                        g_current_lot_size = Fixed_Lot_Size;
                        Print("Martingale: Max level reached. Resetting lot size to: ", DoubleToString(g_current_lot_size, 2));
                    }
                }
                else
                {
                    g_consecutive_losses = 0;
                    g_current_lot_size = Fixed_Lot_Size;
                    Print("Martingale: Profit detected. Resetting lot size to: ", DoubleToString(g_current_lot_size, 2));
                }
                
                // We only process the very latest closed deal per tick
                break;
            }
        }
    }
}


//+------------------------------------------------------------------+
//| Count open positions for all symbols                              |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
   int count = 0;

   // Loop through all open positions
   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket))
      {
         // Check if the position belongs to our EA
         if(PositionGetInteger(POSITION_MAGIC) == Magic_Number)
         {
            count++;
         }
      }
   }

   return count;
}

//+------------------------------------------------------------------+
//| Check for entry signals on the current symbol                      |
//+------------------------------------------------------------------+
void CheckEntrySignal(bool is_new_bar)
{
   // Check if enough time has passed since the last trade
   if(TimeCurrent() - g_last_trade_time < Min_Trade_Interval)
    {
        if(is_new_bar) Print("DEBUG: Waiting for trade interval. Remaining: ", Min_Trade_Interval - (TimeCurrent() - g_last_trade_time), "s");
        return;
    }

   // Get the EMA values for the last completed candle
   double fast_ema = GetEMA(g_fast_ema_handle, 1);
   double slow_ema = GetEMA(g_slow_ema_handle, 1);

   // Get the close price for the last completed candle
   double close_price = GetClose(1);

   // Skip if we couldn't get valid values
   if(fast_ema < 0 || slow_ema < 0 || close_price < 0)
    {
        if(is_new_bar) Print("DEBUG: Invalid indicator data.");
        return;
    }

    if(is_new_bar) Print("DEBUG: Fast EMA: ", DoubleToString(fast_ema, 5), ", Slow EMA: ", DoubleToString(slow_ema, 5), ", Close: ", DoubleToString(close_price, 5));

   // Entry logic
   if(fast_ema > slow_ema)
    {
        if(close_price > fast_ema)
        {
            if(is_new_bar) Print("DEBUG: Buy condition met. Executing buy trade.");
            ExecuteBuyTrade();
        }
        else
        {
            if(is_new_bar) Print("DEBUG: Buy condition not met. Close is not above fast EMA.");
        }
    }
   else if(fast_ema < slow_ema)
    {
        if(close_price < fast_ema)
        {
            if(is_new_bar) Print("DEBUG: Sell condition met. Executing sell trade.");
            ExecuteSellTrade();
        }
        else
        {
            if(is_new_bar) Print("DEBUG: Sell condition not met. Close is not below fast EMA.");
        }
    }
    else
    {
        if(is_new_bar) Print("DEBUG: No trade condition met. EMAs are equal.");
    }
}

//+------------------------------------------------------------------+
//| Get EMA value for a given handle and shift                        |
//+------------------------------------------------------------------+
double GetEMA(int handle, int shift)
{
   if(handle == INVALID_HANDLE)
   {
      Print("EMA indicator handle is invalid.");
      return -1;
   }

   double buffer[];
   if(CopyBuffer(handle, 0, shift, 1, buffer) <= 0)
   {
      Print("Failed to copy EMA buffer. Error: ", GetLastError());
      return -1;
   }

   return buffer[0];
}

//+------------------------------------------------------------------+
//| Get close price for current symbol and shift                      |
//+------------------------------------------------------------------+
double GetClose(int shift)
{
   double close_array[];
   if(CopyClose(g_symbol, PERIOD_M1, shift, 1, close_array) <= 0)
   {
      Print("Failed to copy close price for ", g_symbol, ". Error: ", GetLastError());
      return -1;
   }

   return close_array[0];
}

//+------------------------------------------------------------------+
//| Execute a buy trade                                               |
//+------------------------------------------------------------------+
void ExecuteBuyTrade()
{
   // Free margin check
   double lotSize = Use_Martingale ? g_current_lot_size : Fixed_Lot_Size;
   Print("Executing BUY trade with lot size: ", DoubleToString(lotSize, 2));
   double margin_required = 0.0;
   if(!OrderCalcMargin(ORDER_TYPE_BUY, g_symbol, lotSize, SymbolInfoDouble(g_symbol, SYMBOL_ASK), margin_required))
   {
      Print("Failed to calculate margin for BUY trade. Error: ", GetLastError());
      return;
   }
   if(AccountInfoDouble(ACCOUNT_MARGIN_FREE) < margin_required)
   {
      Print("Not enough free margin for BUY trade. Required: ", margin_required, ", Available: ", AccountInfoDouble(ACCOUNT_MARGIN_FREE));
      return;
   }

   // Get current price
   double ask = SymbolInfoDouble(g_symbol, SYMBOL_ASK);
   if(ask == 0) return;

   // Calculate TP/SL levels based on user preference
   double stopLossPrice, takeProfitPrice;
   if(Use_ATR_Based_TPSL)
   {
      stopLossPrice = CalculateATRBasedBuyStopLoss(ask);
      takeProfitPrice = CalculateATRBasedBuyTakeProfit(ask);
   }
   else
   {
      stopLossPrice = CalculateFixedBuyStopLoss(ask);
      takeProfitPrice = CalculateFixedBuyTakeProfit(ask);
   }

   // Execute buy order
   if(trade.Buy(lotSize, g_symbol, ask, stopLossPrice, takeProfitPrice, "EMA Strategy"))
   {
      g_last_trade_time = TimeCurrent();
      Print("BUY order executed successfully.");
   }
   else
   {
      Print("Failed to execute buy order on ", g_symbol, ". Error: ", GetLastError(), " - ", ErrorDescription(GetLastError()));
   }
}

//+------------------------------------------------------------------+
//| Execute a sell trade                                              |
//+------------------------------------------------------------------+
void ExecuteSellTrade()
{
   // Free margin check
   double lotSize = Use_Martingale ? g_current_lot_size : Fixed_Lot_Size;
   Print("Executing SELL trade with lot size: ", DoubleToString(lotSize, 2));
   double margin_required = 0.0;
   if(!OrderCalcMargin(ORDER_TYPE_SELL, g_symbol, lotSize, SymbolInfoDouble(g_symbol, SYMBOL_BID), margin_required))
   {
      Print("Failed to calculate margin for SELL trade. Error: ", GetLastError());
      return;
   }
   if(AccountInfoDouble(ACCOUNT_MARGIN_FREE) < margin_required)
   {
      Print("Not enough free margin for SELL trade. Required: ", margin_required, ", Available: ", AccountInfoDouble(ACCOUNT_MARGIN_FREE));
      return;
   }

   // Get current price
   double bid = SymbolInfoDouble(g_symbol, SYMBOL_BID);
   if(bid == 0) return;

   // Calculate TP/SL levels based on user preference
   double stopLossPrice, takeProfitPrice;
   if(Use_ATR_Based_TPSL)
   {
      stopLossPrice = CalculateATRBasedSellStopLoss(bid);
      takeProfitPrice = CalculateATRBasedSellTakeProfit(bid);
   }
   else
   {
      stopLossPrice = CalculateFixedSellStopLoss(bid);
      takeProfitPrice = CalculateFixedSellTakeProfit(bid);
   }

   // Execute sell order
   if(trade.Sell(lotSize, g_symbol, bid, stopLossPrice, takeProfitPrice, "EMA Strategy"))
   {
      g_last_trade_time = TimeCurrent();
      Print("SELL order executed successfully.");
   }
   else
   {
      Print("Failed to execute sell order on ", g_symbol, ". Error: ", GetLastError(), " - ", ErrorDescription(GetLastError()));
   }
}

//+------------------------------------------------------------------+
//| Validate input parameters                                         |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
   // Check Fixed_Lot_Size and Max_Lot_Size
   double minLot = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_STEP);

   if(Fixed_Lot_Size < minLot || Fixed_Lot_Size > maxLot)
   {
      Print("Invalid Fixed_Lot_Size: ", Fixed_Lot_Size, ". Must be between ", minLot, " and ", maxLot);
      return false;
   }

   if(Max_Lot_Size < minLot || Max_Lot_Size > maxLot)
   {
      Print("Invalid Max_Lot_Size: ", Max_Lot_Size, ". Must be between ", minLot, " and ", maxLot);
      return false;
   }

   // Check if lot sizes are multiples of lot step
   if(MathAbs(Fixed_Lot_Size - MathRound(Fixed_Lot_Size / lotStep) * lotStep) > 0.0000001)
   {
      Print("Invalid Fixed_Lot_Size: ", Fixed_Lot_Size, ". Must be a multiple of ", lotStep);
      return false;
   }

   if(MathAbs(Max_Lot_Size - MathRound(Max_Lot_Size / lotStep) * lotStep) > 0.0000001)
   {
      Print("Invalid Max_Lot_Size: ", Max_Lot_Size, ". Must be a multiple of ", lotStep);
      return false;
   }

   // Check EMA_Period
   if(Fast_EMA_Period <= 0 || Slow_EMA_Period <= 0)
   {
      Print("Invalid EMA_Period. Must be greater than 0.");
      return false;
   }
   
   if(Fast_EMA_Period >= Slow_EMA_Period)
   {
       Print("Fast EMA period must be less than Slow EMA period.");
       return false;
   }

   // Check TP_Pips and SL_Pips
   if(TP_Pips <= 0)
   {
      Print("Invalid TP_Pips: ", TP_Pips, ". Must be greater than 0.");
      return false;
   }

   if(SL_Pips <= 0)
   {
      Print("Invalid SL_Pips: ", SL_Pips, ". Must be greater than 0.");
      return false;
   }

   // Check Min_Trade_Interval
   if(Min_Trade_Interval < 0)
   {
      Print("Invalid Min_Trade_Interval: ", Min_Trade_Interval, ". Must be greater than or equal to 0.");
      return false;
   }

   // Check ATR parameters if ATR-based TP/SL is enabled
   if(Use_ATR_Based_TPSL)
   {
      if(ATR_Period <= 0)
      {
         Print("Invalid ATR_Period: ", ATR_Period, ". Must be greater than 0.");
         return false;
      }

      if(ATR_TP_Multiplier <= 0)
      {
         Print("Invalid ATR_TP_Multiplier: ", ATR_TP_Multiplier, ". Must be greater than 0.");
         return false;
      }

      if(ATR_SL_Multiplier <= 0)
      {
         Print("Invalid ATR_SL_Multiplier: ", ATR_SL_Multiplier, ". Must be greater than 0.");
         return false;
      }

      if(ATR_TP_Multiplier <= ATR_SL_Multiplier)
      {
         Print("Invalid ATR multipliers: TP multiplier (", ATR_TP_Multiplier,
               ") must be greater than SL multiplier (", ATR_SL_Multiplier, ").");
         return false;
      }
   }

   // Check Martingale parameters
   if(Use_Martingale)
   {
      if(Martingale_Multiplier <= 1.0)
      {
         Print("Invalid Martingale_Multiplier: ", Martingale_Multiplier, ". Must be greater than 1.0.");
         return false;
      }

      if(Max_Martingale_Level <= 0)
      {
         Print("Invalid Max_Martingale_Level: ", Max_Martingale_Level, ". Must be greater than 0.");
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Get error description                                             |
//+------------------------------------------------------------------+
string ErrorDescription(int error_code)
{
   string error_string;

   switch(error_code)
   {
      case 0:   error_string = "No error"; break;
      case 4051: error_string = "Invalid function parameter value"; break;
      case 4062: error_string = "Trading disabled"; break;
      case 4063: error_string = "Not enough money"; break;
      case 4073: error_string = "Invalid volume"; break;
      case 4074: error_string = "No quotes to process request"; break;
      case 4077: error_string = "Invalid price"; break;
      case 4099: error_string = "Pending order placed"; break;
      case 4107: error_string = "Invalid price"; break;
      case 4109: error_string = "Invalid SL or TP"; break;
      case 4110: error_string = "Autotrading disabled"; break;
      case 4111: error_string = "Longs not allowed"; break;
      case 4112: error_string = "Shorts not allowed"; break;
      case 4200: error_string = "Order already exists"; break;
      case 4203: error_string = "Max orders reached"; break;
      default:   error_string = "Unknown error"; break;
   }

   return error_string;
}

//+------------------------------------------------------------------+
//| Get the pip value for the current symbol                          |
//+------------------------------------------------------------------+
double GetPipValue()
{
   return g_pip_value;
}

//+------------------------------------------------------------------+
//| Check if the symbol is a gold instrument                          |
//+------------------------------------------------------------------+
bool IsGoldSymbol(string symbol)
{
   // Convert to uppercase for case-insensitive comparison
   string upperSymbol = symbol;
   StringToUpper(upperSymbol);

   // Check for common gold symbol names
   if(StringFind(upperSymbol, "XAU") >= 0 ||
      StringFind(upperSymbol, "GOLD") >= 0 ||
      StringFind(upperSymbol, "XAUUSD") >= 0)
   {
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Get ATR value for current symbol and shift                        |
//+------------------------------------------------------------------+
double GetATR(int shift)
{
   // Check if ATR handle is valid
   if(g_atr_handle == INVALID_HANDLE)
   {
      Print("ATR indicator handle is invalid. Trying to recreate.");
      g_atr_handle = iATR(g_symbol, PERIOD_M1, ATR_Period);
      if(g_atr_handle == INVALID_HANDLE)
      {
         Print("Failed to recreate ATR indicator handle. Error: ", GetLastError());
         return -1;
      }
   }

   // Get the ATR value
   double buffer[];
   if(CopyBuffer(g_atr_handle, 0, shift, 1, buffer) <= 0)
   {
      Print("Failed to copy ATR buffer for ", g_symbol, ". Error: ", GetLastError());
      return -1;
   }

   return buffer[0];
}

//+------------------------------------------------------------------+
//| Calculate fixed stop loss for buy order                          |
//+------------------------------------------------------------------+
double CalculateFixedBuyStopLoss(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();

   // Calculate stop loss using fixed pips
   double stopLossPrice = NormalizeDouble(entry_price - (SL_Pips * pip_value), digits);

   return stopLossPrice;
}

//+------------------------------------------------------------------+
//| Calculate fixed take profit for buy order                        |
//+------------------------------------------------------------------+
double CalculateFixedBuyTakeProfit(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();

   // Calculate take profit using fixed pips
   double takeProfitPrice = NormalizeDouble(entry_price + (TP_Pips * pip_value), digits);

   return takeProfitPrice;
}

//+------------------------------------------------------------------+
//| Calculate fixed stop loss for sell order                         |
//+------------------------------------------------------------------+
double CalculateFixedSellStopLoss(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();

   // Calculate stop loss using fixed pips
   double stopLossPrice = NormalizeDouble(entry_price + (SL_Pips * pip_value), digits);

   return stopLossPrice;
}

//+------------------------------------------------------------------+
//| Calculate fixed take profit for sell order                       |
//+------------------------------------------------------------------+
double CalculateFixedSellTakeProfit(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();

   // Calculate take profit using fixed pips
   double takeProfitPrice = NormalizeDouble(entry_price - (TP_Pips * pip_value), digits);

   return takeProfitPrice;
}

//+------------------------------------------------------------------+
//| Calculate ATR-based stop loss for buy order                      |
//+------------------------------------------------------------------+
double CalculateATRBasedBuyStopLoss(double entry_price)
{
   double atr_value = GetATR(1); // Get ATR from last completed candle
   if(atr_value <= 0)
   {
      Print("Invalid ATR value, falling back to fixed SL");
      return CalculateFixedBuyStopLoss(entry_price);
   }

   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);

   // Calculate stop loss using ATR multiplier
   double stopLossPrice = NormalizeDouble(entry_price - (atr_value * ATR_SL_Multiplier), digits);

   return stopLossPrice;
}

//+------------------------------------------------------------------+
//| Calculate ATR-based take profit for buy order                    |
//+------------------------------------------------------------------+
double CalculateATRBasedBuyTakeProfit(double entry_price)
{
   double atr_value = GetATR(1); // Get ATR from last completed candle
   if(atr_value <= 0)
   {
      Print("Invalid ATR value, falling back to fixed TP");
      return CalculateFixedBuyTakeProfit(entry_price);
   }

   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);

   // Calculate take profit using ATR multiplier
   double takeProfitPrice = NormalizeDouble(entry_price + (atr_value * ATR_TP_Multiplier), digits);

   return takeProfitPrice;
}

//+------------------------------------------------------------------+
//| Calculate ATR-based stop loss for sell order                     |
//+------------------------------------------------------------------+
double CalculateATRBasedSellStopLoss(double entry_price)
{
   double atr_value = GetATR(1); // Get ATR from last completed candle
   if(atr_value <= 0)
   {
      Print("Invalid ATR value, falling back to fixed SL");
      return CalculateFixedSellStopLoss(entry_price);
   }

   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);

   // Calculate stop loss using ATR multiplier
   double stopLossPrice = NormalizeDouble(entry_price + (atr_value * ATR_SL_Multiplier), digits);

   return stopLossPrice;
}

//+------------------------------------------------------------------+
//| Calculate ATR-based take profit for sell order                   |
//+------------------------------------------------------------------+
double CalculateATRBasedSellTakeProfit(double entry_price)
{
   double atr_value = GetATR(1); // Get ATR from last completed candle
   if(atr_value <= 0)
   {
      Print("Invalid ATR value, falling back to fixed TP");
      return CalculateFixedSellTakeProfit(entry_price);
   }

   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);

   // Calculate take profit using ATR multiplier
   double takeProfitPrice = NormalizeDouble(entry_price - (atr_value * ATR_TP_Multiplier), digits);

   return takeProfitPrice;
}
